Conditions:
  IsProdStage: !Equals ["${self:provider.stage}", "prod"]

Resources:
  OapBackendECRLifecyclePolicy:
    Type: AWS::ECR::LifecyclePolicy
    Properties:
      RepositoryName: serverless-oap-backend-service-${self:provider.stage}
      LifecyclePolicyText: !If
        - IsProdStage
        - |
          {
            "rules": [
              {
                "rulePriority": 1,
                "description": "Image expiry rules",
                "selection": {
                  "tagStatus": "any",
                  "countType": "imageCountMoreThan",
                  "countNumber": 10
                },
                "action": {
                  "type": "expire"
                }
              }
            ]
          }
        - |
          {
            "rules": [
              {
                "rulePriority": 1,
                "description": "Image expiry rules",
                "selection": {
                  "tagStatus": "any",
                  "countType": "imageCountMoreThan",
                  "countNumber": 3
                },
                "action": {
                  "type": "expire"
                }
              }
            ]
          }
